import React, { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "./ui/button";
import { X, Upload, Image, ChevronDown, Edit3 } from "lucide-react";
import { useAuth } from "../hooks/useAuth";
import { supabase } from "../lib/supabase";
import { detectSmartTag, detectSmartTagSync, loadTagRules } from "../lib/smartTagDetection";

interface EditBlockModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpdateBlock: (blockData: {
    id: string;
    name: string;
    smart_tag: string;
    banner_gradient?: string;
    custom_banner_image?: string | null;
  }) => void;
  block: {
    id: string;
    name: string;
    smart_tag: string;
    banner_gradient: string;
    custom_banner_image?: string | null;
  } | null;
}

const GRADIENT_OPTIONS = [
  { name: "Sunset", value: "from-orange-400 via-red-500 to-pink-500" },
  { name: "Ocean", value: "from-blue-400 via-blue-500 to-blue-600" },
  { name: "<PERSON>", value: "from-green-400 via-green-500 to-green-600" },
  { name: "Purple", value: "from-purple-400 via-purple-500 to-purple-600" },
  { name: "Gold", value: "from-yellow-400 via-orange-500 to-red-500" },
  { name: "Mint", value: "from-green-300 via-blue-500 to-purple-600" },
  { name: "Rose", value: "from-pink-300 via-purple-300 to-indigo-400" },
  { name: "Slate", value: "from-slate-400 via-slate-500 to-slate-600" }
];



export const EditBlockModal: React.FC<EditBlockModalProps> = ({
  isOpen,
  onClose,
  onUpdateBlock,
  block
}) => {
  const { user } = useAuth();
  const [blockName, setBlockName] = useState("");
  const [customTag, setCustomTag] = useState("");
  const [selectedGradient, setSelectedGradient] = useState(GRADIENT_OPTIONS[0].value);
  const [customBannerImage, setCustomBannerImage] = useState<string | null>(null);
  const [useCustomBanner, setUseCustomBanner] = useState(false);
  const [existingTags, setExistingTags] = useState<string[]>([]);
  const [showTagDropdown, setShowTagDropdown] = useState(false);
  const [tagInputMode, setTagInputMode] = useState<'existing' | 'custom'>('existing');
  const [detectedTag, setDetectedTag] = useState<string>('General');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Initialize form when block changes
  useEffect(() => {
    if (block) {
      setBlockName(block.name);
      setCustomTag(block.smart_tag);
      setSelectedGradient(block.banner_gradient);
      setCustomBannerImage(block.custom_banner_image || null);
      setUseCustomBanner(!!block.custom_banner_image);
    }
  }, [block]);

  // Load existing tags when modal opens
  useEffect(() => {
    if (isOpen && user) {
      console.log('EditBlockModal: Initializing for user:', user.uid);
      loadExistingTags().catch(error => {
        console.log('EditBlockModal: Loading existing tags failed:', error);
      });
    }
  }, [isOpen, user]);

  // Update detected tag when block name changes
  useEffect(() => {
    if (blockName.trim()) {
      try {
        // Use only sync version to avoid async errors
        const syncTag = detectSmartTagSync(blockName);
        setDetectedTag(syncTag);
      } catch (error) {
        console.log('Tag detection failed, using General:', error);
        setDetectedTag('General');
      }
    } else {
      setDetectedTag('General');
    }
  }, [blockName]);

  const loadExistingTags = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('vault_blocks')
        .select('smart_tag')
        .eq('user_id', user.uid);

      if (error) {
        console.error('Error loading existing tags:', error);
        return;
      }

      // Extract unique tags
      const uniqueTags = [...new Set(data.map(block => block.smart_tag).filter(Boolean))];
      setExistingTags(uniqueTags);
    } catch (error) {
      console.error('Error loading existing tags:', error);
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setCustomBannerImage(result);
        setUseCustomBanner(true);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = () => {
    if (!blockName.trim() || !block) return;

    const finalTag = customTag.trim() || detectedTag;

    onUpdateBlock({
      id: block.id,
      name: blockName.trim(),
      smart_tag: finalTag,
      banner_gradient: selectedGradient,
      custom_banner_image: useCustomBanner ? customBannerImage : null
    });

    onClose();
  };

  const handleClose = () => {
    setShowTagDropdown(false);
    setTagInputMode('existing');
    onClose();
  };

  if (!isOpen || !block) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[9999]">
      <div className="bg-white/90 backdrop-blur-md border border-white/30 rounded-2xl shadow-2xl w-full max-w-lg mx-4 p-6 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-cormorant font-bold text-[#2E0406]">Edit Block</h2>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleClose}
            className="w-8 h-8 rounded-full hover:bg-[#8b2635]/10 transition-all duration-200"
          >
            <X className="w-4 h-4 text-[#8b2635]" />
          </Button>
        </div>

        <div className="space-y-4">
          {/* Block Name */}
          <div>
            <label className="block text-sm font-poppins font-medium text-[#2E0406] mb-2">
              Block Name *
            </label>
            <input
              type="text"
              value={blockName}
              onChange={(e) => setBlockName(e.target.value)}
              placeholder="Enter block name..."
              className="w-full px-4 py-3 bg-white/60 border border-[#e0d7cc]/40 rounded-xl text-[#2E0406] placeholder-[#8b7355] font-poppins focus:outline-none focus:ring-2 focus:ring-[#8b2635]/30 focus:border-[#8b2635] transition-all duration-200"
              autoFocus
            />
          </div>

          {/* Category Tag */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="text-sm font-poppins font-medium text-[#2E0406]">
                Category Tag <span className="text-[#8b7355] text-xs">(optional - auto-detected if empty)</span>
              </label>
              <div className="flex bg-[#e0d7cc]/20 rounded-lg p-1">
                <button
                  type="button"
                  onClick={() => setTagInputMode('existing')}
                  className={`px-3 py-1 text-xs font-poppins rounded transition-all duration-200 ${
                    tagInputMode === 'existing'
                      ? 'bg-[#8b2635] text-white'
                      : 'text-[#8b7355] hover:text-[#8b2635]'
                  }`}
                >
                  Existing
                </button>
                <button
                  type="button"
                  onClick={() => setTagInputMode('custom')}
                  className={`px-3 py-1 text-xs font-poppins rounded transition-all duration-200 ${
                    tagInputMode === 'custom'
                      ? 'bg-[#8b2635] text-white'
                      : 'text-[#8b7355] hover:text-[#8b2635]'
                  }`}
                >
                  Custom
                </button>
              </div>
            </div>

            {tagInputMode === 'existing' ? (
              <div className="relative">
                <button
                  type="button"
                  onClick={() => setShowTagDropdown(!showTagDropdown)}
                  className="w-full px-4 py-3 bg-white/60 border border-[#e0d7cc]/40 rounded-xl text-[#2E0406] font-poppins focus:outline-none focus:ring-2 focus:ring-[#8b2635]/30 focus:border-[#8b2635] transition-all duration-200 flex items-center justify-between"
                >
                  <span className={customTag ? 'text-[#2E0406]' : 'text-[#8b7355]'}>
                    {customTag || 'Select existing tag...'}
                  </span>
                  <ChevronDown className={`w-4 h-4 text-[#8b7355] transition-transform duration-200 ${showTagDropdown ? 'rotate-180' : ''}`} />
                </button>

                {showTagDropdown && (
                  <div className="absolute top-full left-0 right-0 mt-1 bg-white/90 backdrop-blur-md border border-[#e0d7cc]/40 rounded-xl shadow-lg max-h-40 overflow-y-auto z-50">
                    {existingTags.length > 0 ? (
                      existingTags.map((tag) => (
                        <button
                          key={tag}
                          type="button"
                          onClick={() => {
                            setCustomTag(tag);
                            setShowTagDropdown(false);
                          }}
                          className="w-full text-left px-4 py-2 text-[#2E0406] font-poppins text-sm hover:bg-[#8b2635]/10 transition-colors"
                        >
                          {tag}
                        </button>
                      ))
                    ) : (
                      <div className="px-4 py-2 text-[#8b7355] font-poppins text-sm">
                        No existing tags found
                      </div>
                    )}
                  </div>
                )}
              </div>
            ) : (
              <input
                type="text"
                value={customTag}
                onChange={(e) => setCustomTag(e.target.value)}
                placeholder="e.g., History, Science, Art..."
                className="w-full px-4 py-3 bg-white/60 border border-[#e0d7cc]/40 rounded-xl text-[#2E0406] placeholder-[#8b7355] font-poppins focus:outline-none focus:ring-2 focus:ring-[#8b2635]/30 focus:border-[#8b2635] transition-all duration-200"
              />
            )}

            {blockName && !customTag && (
              <p className="text-xs text-[#8b7355] mt-1 font-poppins">
                Will auto-detect as: <span className="font-medium text-[#8b2635]">{detectedTag}</span>
              </p>
            )}
          </div>

          {/* Banner Customization */}
          <div>
            <label className="block text-sm font-poppins font-medium text-[#2E0406] mb-3">
              Banner Style
            </label>

            {/* Banner Type Toggle */}
            <div className="flex bg-[#e0d7cc]/20 rounded-lg p-1 mb-4">
              <button
                type="button"
                onClick={() => setUseCustomBanner(false)}
                className={`flex-1 px-3 py-2 text-sm font-poppins rounded transition-all duration-200 ${
                  !useCustomBanner
                    ? 'bg-[#8b2635] text-white'
                    : 'text-[#8b7355] hover:text-[#8b2635]'
                }`}
              >
                Gradient
              </button>
              <button
                type="button"
                onClick={() => setUseCustomBanner(true)}
                className={`flex-1 px-3 py-2 text-sm font-poppins rounded transition-all duration-200 ${
                  useCustomBanner
                    ? 'bg-[#8b2635] text-white'
                    : 'text-[#8b7355] hover:text-[#8b2635]'
                }`}
              >
                Custom Image
              </button>
            </div>

            {!useCustomBanner ? (
              /* Gradient Options */
              <div className="grid grid-cols-4 gap-2">
                {GRADIENT_OPTIONS.map((gradient) => (
                  <button
                    key={gradient.value}
                    type="button"
                    onClick={() => setSelectedGradient(gradient.value)}
                    className={`relative h-12 rounded-lg bg-gradient-to-r ${gradient.value} transition-all duration-200 ${
                      selectedGradient === gradient.value
                        ? 'ring-2 ring-[#8b2635] ring-offset-2'
                        : 'hover:scale-105'
                    }`}
                    title={gradient.name}
                  >
                    {selectedGradient === gradient.value && (
                      <div className="absolute inset-0 bg-white/20 rounded-lg flex items-center justify-center">
                        <div className="w-3 h-3 bg-white rounded-full" />
                      </div>
                    )}
                  </button>
                ))}
              </div>
            ) : (
              /* Custom Image Upload */
              <div className="space-y-3">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />

                <Button
                  type="button"
                  onClick={() => fileInputRef.current?.click()}
                  variant="outline"
                  className="w-full border-[#e0d7cc] text-[#8b7355] hover:bg-[#e0d7cc]/20 rounded-xl h-12 font-poppins"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  {customBannerImage ? 'Change Image' : 'Upload Image'}
                </Button>

                {customBannerImage && (
                  <div className="relative h-20 rounded-lg overflow-hidden">
                    <img
                      src={customBannerImage}
                      alt="Banner preview"
                      className="w-full h-full object-cover"
                    />
                    <button
                      type="button"
                      onClick={() => {
                        setCustomBannerImage(null);
                        setUseCustomBanner(false);
                      }}
                      className="absolute top-1 right-1 w-6 h-6 bg-black/50 hover:bg-black/70 rounded-full flex items-center justify-center transition-colors"
                    >
                      <X className="w-3 h-3 text-white" />
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Preview */}
          <div>
            <label className="block text-sm font-poppins font-medium text-[#2E0406] mb-2">
              Preview
            </label>
            <div className="bg-white/60 border border-[#e0d7cc]/40 rounded-xl overflow-hidden">
              {/* Banner Preview */}
              <div className="relative h-16 overflow-hidden">
                {useCustomBanner && customBannerImage ? (
                  <img
                    src={customBannerImage}
                    alt="Banner preview"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className={`w-full h-full bg-gradient-to-r ${selectedGradient}`} />
                )}
                <div className="absolute inset-0 bg-gradient-to-br from-transparent via-black/10 to-black/20" />
                <div className="absolute bottom-1 left-2">
                  <div className="bg-white/80 backdrop-blur-sm rounded px-2 py-0.5">
                    <span className="text-[#8b2635] font-poppins text-xs font-medium">
                      {customTag.trim() || detectSmartTag(blockName)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Preview Content */}
              <div className="p-3">
                <h3 className="font-cormorant font-bold text-[#2E0406] text-base mb-1">
                  {blockName || "Block Name"}
                </h3>
                <div className="text-xs text-[#6d1f2c]">
                  <span className="font-poppins">0 items</span>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-2">
            <Button
              onClick={handleClose}
              variant="outline"
              className="flex-1 border-[#e0d7cc] text-[#8b7355] hover:bg-[#e0d7cc]/20 rounded-xl h-10 font-poppins"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={!blockName.trim()}
              className="flex-1 bg-gradient-to-r from-[#8b2635] to-[#6d1f2c] hover:from-[#6d1f2c] hover:to-[#8b2635] text-white border-0 rounded-xl h-10 font-poppins disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              <Edit3 className="w-4 h-4 mr-2" />
              Update Block
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
