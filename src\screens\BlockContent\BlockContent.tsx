import React, { useState, useRef, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Button } from "../../components/ui/button";
import { useAuth } from "../../hooks/useAuth";
import { LoadingScreen } from "../../components/ui/loading-screen";
import { supabase } from "../../lib/supabase";
import {
  Home as HomeIcon,
  Archive,
  Brain,
  Map,
  Trophy,
  LogOut,
  Search,
  Bell,
  User,
  X,
  Menu,
  ChevronRight,
  Plus,
  FileText,
  Edit3,
  MoreVertical,
  Pin,
  Trash2,
  ArrowLeft,
  Folder,
  FolderOpen
} from "lucide-react";

interface VaultBlock {
  id: string;
  user_id: string;
  parent_id?: string;
  name: string;
  smart_tag: string;
  banner_gradient: string;
  custom_banner_image?: string;
  is_pinned: boolean;
  total_items: number;
  position: number;
  depth: number;
  created_at: string;
  updated_at: string;
}

interface BlockContent {
  id: string;
  block_id: string;
  title: string;
  content: string;
  content_type: 'text' | 'image' | 'video' | 'audio' | 'file';
  position: number;
  created_at: string;
  updated_at: string;
}

export const BlockContent = (): JSX.Element => {
  const { blockId } = useParams<{ blockId: string }>();
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [loading, setLoading] = useState(true);
  const [logoutLoading, setLogoutLoading] = useState(false);
  const [currentBlock, setCurrentBlock] = useState<VaultBlock | null>(null);
  const [breadcrumb, setBreadcrumb] = useState<VaultBlock[]>([]);
  const [subBlocks, setSubBlocks] = useState<VaultBlock[]>([]);
  const [blockContents, setBlockContents] = useState<BlockContent[]>([]);
  const [showAddBlockModal, setShowAddBlockModal] = useState(false);
  const [showAddContentModal, setShowAddContentModal] = useState(false);
  const [newBlockName, setNewBlockName] = useState("");
  const [newContentTitle, setNewContentTitle] = useState("");
  const [newContentText, setNewContentText] = useState("");

  const userDropdownRef = useRef<HTMLDivElement>(null);
  const notificationDropdownRef = useRef<HTMLDivElement>(null);

  // Load block data
  useEffect(() => {
    if (user && blockId) {
      loadBlockData();
    }
  }, [user, blockId]);

  const loadBlockData = async () => {
    if (!user || !blockId) return;

    try {
      setLoading(true);

      // Load current block
      const { data: blockData, error: blockError } = await supabase
        .from('vault_blocks')
        .select('*')
        .eq('id', blockId)
        .eq('user_id', user.uid)
        .single();

      if (blockError) {
        console.error('Error loading block:', blockError);
        navigate('/my-vault');
        return;
      }

      setCurrentBlock(blockData);

      // Build breadcrumb
      await buildBreadcrumb(blockData);

      // Load sub-blocks
      const { data: subBlocksData, error: subBlocksError } = await supabase
        .from('vault_blocks')
        .select('*')
        .eq('parent_id', blockId)
        .eq('user_id', user.uid)
        .order('position', { ascending: true });

      if (subBlocksError) {
        console.error('Error loading sub-blocks:', subBlocksError);
      } else {
        setSubBlocks(subBlocksData || []);
      }

      // Load block contents (placeholder for now)
      setBlockContents([]);

    } catch (error) {
      console.error('Error loading block data:', error);
    } finally {
      setLoading(false);
    }
  };

  const buildBreadcrumb = async (block: VaultBlock) => {
    const breadcrumbPath: VaultBlock[] = [];
    let currentBlock = block;

    // Build path from current block to root
    while (currentBlock.parent_id) {
      const { data: parentData, error } = await supabase
        .from('vault_blocks')
        .select('*')
        .eq('id', currentBlock.parent_id)
        .eq('user_id', user!.uid)
        .single();

      if (error || !parentData) break;
      
      breadcrumbPath.unshift(parentData);
      currentBlock = parentData;
    }

    setBreadcrumb(breadcrumbPath);
  };

  const createSubBlock = async () => {
    if (!user || !currentBlock || !newBlockName.trim()) return;

    try {
      const newBlockData = {
        user_id: user.uid,
        parent_id: currentBlock.id,
        name: newBlockName.trim(),
        smart_tag: currentBlock.smart_tag,
        banner_gradient: currentBlock.banner_gradient,
        is_pinned: false,
        total_items: 0,
        position: subBlocks.length,
        depth: currentBlock.depth + 1
      };

      const { data, error } = await supabase
        .from('vault_blocks')
        .insert([newBlockData])
        .select()
        .single();

      if (error) {
        console.error('Error creating sub-block:', error);
        return;
      }

      setSubBlocks([...subBlocks, data]);
      setNewBlockName("");
      setShowAddBlockModal(false);

      // Update parent's total_items count
      await supabase
        .from('vault_blocks')
        .update({ total_items: subBlocks.length + 1 })
        .eq('id', currentBlock.id);

    } catch (error) {
      console.error('Error creating sub-block:', error);
    }
  };

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  const handleLogout = async () => {
    setLogoutLoading(true);
    try {
      await logout();
      navigate("/login");
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      setLogoutLoading(false);
    }
  };

  const sidebarItems = [
    { icon: HomeIcon, label: "home", active: false, path: "/home" },
    { icon: Archive, label: "my vault", active: true, path: "/my-vault" },
    { icon: Brain, label: "mind games", active: false, path: "/mind-games" },
    { icon: Map, label: "visual maps", active: false, path: "/visual-maps" },
    { icon: Trophy, label: "achievements", active: false, path: "/achievements" },
  ];

  if (loading) {
    return <LoadingScreen message="Loading block content..." />;
  }

  if (!currentBlock) {
    return <LoadingScreen message="Block not found..." />;
  }

  return (
    <>
      {logoutLoading && <LoadingScreen message="signing you out..." />}
      <div
        className="min-h-screen w-full font-sans relative"
        style={{
          backgroundImage: 'url(/background.jpg)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          backgroundAttachment: 'fixed'
        }}
      >
        {/* Subtle overlay to tone down the background */}
        <div className="absolute inset-0 bg-[#2E0406]/40 backdrop-blur-[0.5px]" />

        {/* Top Navigation Bar */}
        <div className="w-full px-3 py-6 relative z-10">
          <div className="bg-[#faf7f2]/90 backdrop-blur-md border border-[#e0d7cc]/60 rounded-2xl shadow-lg p-4 relative z-50 max-w-[90%] mx-auto">
            <div className="flex items-center justify-between">
              {/* Left: Back button, Hamburger/Close and Logo */}
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => navigate('/my-vault')}
                  className="w-9 h-9 rounded-xl hover:bg-[#d4c7b8]/40 transition-all duration-200"
                >
                  <ArrowLeft className="w-4 h-4 text-[#5a4a3a]/80" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSidebarOpen(!sidebarOpen)}
                  className="w-9 h-9 rounded-xl hover:bg-[#d4c7b8]/40 transition-all duration-200"
                >
                  {sidebarOpen ? (
                    <X className="w-4 h-4 text-[#5a4a3a]/80" />
                  ) : (
                    <Menu className="w-4 h-4 text-[#5a4a3a]/80" />
                  )}
                </Button>
                <div className="flex items-center gap-3">
                  <img
                    src="/app logo.png"
                    alt="Logo"
                    className="w-9 h-9 rounded-xl object-cover shadow-sm"
                  />
                </div>
              </div>

              {/* Center: Breadcrumb */}
              <div className="flex-1 max-w-lg mx-6">
                <div className="flex items-center gap-2 text-sm">
                  <button
                    onClick={() => navigate('/my-vault')}
                    className="text-[#8b7355] hover:text-[#2E0406] transition-colors font-poppins"
                  >
                    My Vault
                  </button>
                  {breadcrumb.map((block, index) => (
                    <React.Fragment key={block.id}>
                      <ChevronRight className="w-4 h-4 text-[#8b7355]" />
                      <button
                        onClick={() => navigate(`/block/${block.id}`)}
                        className="text-[#8b7355] hover:text-[#2E0406] transition-colors font-poppins"
                      >
                        {block.name}
                      </button>
                    </React.Fragment>
                  ))}
                  <ChevronRight className="w-4 h-4 text-[#8b7355]" />
                  <span className="text-[#2E0406] font-poppins font-medium">
                    {currentBlock.name}
                  </span>
                </div>
              </div>

              {/* Right: User controls */}
              <div className="flex items-center gap-3">
                {/* User */}
                <div className="relative flex items-center gap-2" ref={userDropdownRef}>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="w-10 h-10 rounded-full hover:bg-[#d4c7b8]/40 transition-all duration-200 p-0"
                  >
                    {user?.photoURL ? (
                      <img
                        src={user.photoURL}
                        alt="Profile"
                        className="w-8 h-8 rounded-full object-cover"
                      />
                    ) : (
                      <User className="w-5 h-5 text-[#5a4a3a]/80" />
                    )}
                  </Button>
                  <span className="text-sm font-poppins text-[#2E0406]/90">
                    {user?.displayName || 'user'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex px-3 pb-6 h-[calc(100vh-144px)] max-w-[90%] mx-auto relative z-10">
          {/* Sidebar */}
          <div className={`transition-all duration-500 ease-in-out ${
            sidebarOpen ? 'w-52 mr-5' : 'w-16 mr-4'
          }`}>
            <div className="bg-[#faf7f2]/90 backdrop-blur-md border border-[#e0d7cc]/60 rounded-2xl shadow-lg h-full relative overflow-hidden">
              {/* Full Sidebar Content */}
              <div className={`absolute inset-0 p-4 transition-all duration-500 ease-in-out ${
                sidebarOpen ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-[-100%]'
              }`}>
                {/* Navigation Items */}
                <div className="space-y-2 mb-8">
                  {sidebarItems.map((item, index) => (
                    <Button
                      key={index}
                      variant="ghost"
                      onClick={() => handleNavigation(item.path)}
                      className={`w-full justify-start gap-3 h-12 rounded-xl font-poppins text-sm transition-all duration-200 relative ${
                        item.active
                          ? 'bg-[#2E0406]/80 text-[#faf7f2] hover:bg-[#3d1a1c]/80 backdrop-blur-sm border border-[#5a4a3a]/20 shadow-lg'
                          : 'text-[#5a4a3a] hover:bg-[#e0d7cc]/40 hover:text-[#2E0406]'
                      }`}
                    >
                      <item.icon className="w-4 h-4" />
                      <span>{item.label}</span>
                    </Button>
                  ))}
                </div>

                {/* Logout Button */}
                <div className="absolute bottom-4 left-4 right-4">
                  <Button
                    variant="ghost"
                    onClick={handleLogout}
                    className="w-full justify-start gap-3 h-12 rounded-xl font-poppins text-sm text-[#8b2635] hover:bg-[#8b2635]/10 hover:text-[#6d1f2c] transition-all duration-200"
                  >
                    <LogOut className="w-4 h-4" />
                    <span>Logout</span>
                  </Button>
                </div>
              </div>

              {/* Icon-Only Sidebar Content */}
              <div className={`absolute inset-0 p-3 transition-all duration-500 ease-in-out ${
                sidebarOpen ? 'opacity-0 translate-x-[100%]' : 'opacity-100 translate-x-0'
              }`}>
                {/* Navigation Icons */}
                <div className="space-y-2 mb-8">
                  {sidebarItems.map((item, index) => (
                    <Button
                      key={index}
                      variant="ghost"
                      size="icon"
                      onClick={() => handleNavigation(item.path)}
                      className={`w-10 h-10 rounded-xl transition-all duration-200 ${
                        item.active
                          ? 'bg-[#2E0406]/80 text-[#faf7f2] hover:bg-[#3d1a1c]/80'
                          : 'text-[#5a4a3a] hover:bg-[#e0d7cc]/40 hover:text-[#2E0406]'
                      }`}
                      title={item.label}
                    >
                      <item.icon className="w-4 h-4" />
                    </Button>
                  ))}
                </div>

                {/* Logout Icon */}
                <div className="absolute bottom-3 left-3 right-3">
                  <Button
                    onClick={handleLogout}
                    variant="ghost"
                    size="icon"
                    className="w-10 h-10 rounded-xl text-[#8b2635] hover:bg-[#8b2635]/10 transition-all duration-200"
                    title="logout"
                  >
                    <LogOut className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 transition-all duration-500 ease-in-out">
            <div className="bg-gradient-to-br from-[#f8f4ee] via-[#faf7f2] to-[#f5f0e8] h-full overflow-hidden rounded-2xl">
              {/* Scrollable Content Container */}
              <div className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-[#8b2635] scrollbar-track-transparent hover:scrollbar-thumb-[#6d1f2c]">
                <div className="p-8">
                  {/* Block Header */}
                  <div className="mb-8">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h1 className="text-4xl md:text-5xl font-cormorant font-bold text-[#2E0406] mb-2">
                          {currentBlock.name}
                        </h1>
                        <div className="flex items-center gap-4 text-sm text-[#8b7355]">
                          <span className="bg-[#8b2635]/10 text-[#8b2635] px-3 py-1 rounded-full font-poppins">
                            {currentBlock.smart_tag}
                          </span>
                          <span className="font-poppins">
                            {subBlocks.length} sub-blocks
                          </span>
                          <span className="font-poppins">
                            {blockContents.length} items
                          </span>
                        </div>
                      </div>
                      <div className="flex gap-3">
                        <Button
                          onClick={() => setShowAddBlockModal(true)}
                          className="bg-gradient-to-r from-[#8b2635] to-[#6d1f2c] hover:from-[#6d1f2c] hover:to-[#8b2635] text-white border-0 rounded-xl h-12 px-6 font-poppins shadow-lg hover:shadow-xl transition-all duration-200"
                        >
                          <Plus className="w-5 h-5 mr-2" />
                          Add Sub-Block
                        </Button>
                        <Button
                          onClick={() => setShowAddContentModal(true)}
                          variant="outline"
                          className="border-[#8b2635] text-[#8b2635] hover:bg-[#8b2635] hover:text-white rounded-xl h-12 px-6 font-poppins transition-all duration-200"
                        >
                          <FileText className="w-5 h-5 mr-2" />
                          Add Content
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Sub-blocks Grid */}
                  {subBlocks.length > 0 && (
                    <div className="mb-8">
                      <h2 className="text-2xl font-cormorant font-bold text-[#2E0406] mb-6">Sub-blocks</h2>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {subBlocks.map((subBlock) => (
                          <div
                            key={subBlock.id}
                            onClick={() => navigate(`/block/${subBlock.id}`)}
                            className="group bg-white/60 backdrop-blur-sm border border-[#e0d7cc]/40 rounded-2xl hover:shadow-lg hover:scale-105 transition-all duration-300 cursor-pointer relative"
                          >
                            {/* Sub-block Banner */}
                            <div className="h-24 relative overflow-hidden rounded-t-2xl">
                              <div className={`w-full h-full bg-gradient-to-r ${subBlock.banner_gradient}`} />
                              <div className="absolute inset-0 bg-gradient-to-br from-transparent via-black/10 to-black/20" />
                              
                              {subBlock.is_pinned && (
                                <div className="absolute top-2 left-3">
                                  <div className="bg-[#8b2635] rounded-full p-1 shadow-md">
                                    <Pin className="w-2 h-2 text-white" />
                                  </div>
                                </div>
                              )}

                              <div className="absolute bottom-2 left-3">
                                <div className="bg-white/80 backdrop-blur-sm rounded-lg px-2 py-1">
                                  <span className="text-[#8b2635] font-poppins text-xs font-medium">
                                    {subBlock.smart_tag}
                                  </span>
                                </div>
                              </div>
                            </div>

                            {/* Sub-block Content */}
                            <div className="p-4">
                              <div className="flex items-center justify-between mb-2">
                                <h3 className="font-cormorant font-bold text-[#2E0406] text-lg group-hover:text-[#8b2635] transition-colors">
                                  {subBlock.name}
                                </h3>
                                <FolderOpen className="w-5 h-5 text-[#8b2635] opacity-0 group-hover:opacity-100 transition-opacity" />
                              </div>
                              
                              <div className="text-sm text-[#6d1f2c]">
                                <span className="font-poppins">{subBlock.total_items} items</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Content Items */}
                  <div className="mb-8">
                    <h2 className="text-2xl font-cormorant font-bold text-[#2E0406] mb-6">Content</h2>
                    {blockContents.length === 0 ? (
                      <div className="text-center py-16">
                        <div className="w-24 h-24 bg-[#e0d7cc]/40 rounded-3xl flex items-center justify-center mx-auto mb-6">
                          <FileText className="w-12 h-12 text-[#8b7355]" />
                        </div>
                        <h3 className="font-cormorant font-bold text-[#2E0406] text-2xl mb-3">No content yet</h3>
                        <p className="text-[#8b7355] font-poppins mb-6 max-w-md mx-auto">
                          Start adding content to this block to organize your knowledge.
                        </p>
                        <Button
                          onClick={() => setShowAddContentModal(true)}
                          className="bg-gradient-to-r from-[#8b2635] to-[#6d1f2c] hover:from-[#6d1f2c] hover:to-[#8b2635] text-white border-0 rounded-xl h-12 px-8 font-poppins"
                        >
                          <FileText className="w-5 h-5 mr-2" />
                          Add First Content
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {blockContents.map((content) => (
                          <div
                            key={content.id}
                            className="bg-white/60 backdrop-blur-sm border border-[#e0d7cc]/40 rounded-2xl p-6 hover:shadow-lg transition-all duration-300"
                          >
                            <h3 className="font-cormorant font-bold text-[#2E0406] text-xl mb-3">
                              {content.title}
                            </h3>
                            <p className="text-[#6d1f2c] font-poppins">
                              {content.content}
                            </p>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Add Sub-Block Modal */}
        {showAddBlockModal && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[9999]">
            <div className="bg-white/90 backdrop-blur-md border border-white/30 rounded-2xl shadow-2xl w-full max-w-md mx-4 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-cormorant font-bold text-[#2E0406]">Add Sub-Block</h2>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowAddBlockModal(false)}
                  className="w-8 h-8 rounded-full hover:bg-[#8b2635]/10 transition-all duration-200"
                >
                  <X className="w-4 h-4 text-[#8b2635]" />
                </Button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-poppins font-medium text-[#2E0406] mb-2">
                    Block Name
                  </label>
                  <input
                    type="text"
                    value={newBlockName}
                    onChange={(e) => setNewBlockName(e.target.value)}
                    placeholder="Enter block name..."
                    className="w-full px-4 py-3 bg-white/60 border border-[#e0d7cc]/40 rounded-xl text-[#2E0406] placeholder-[#8b7355] font-poppins focus:outline-none focus:ring-2 focus:ring-[#8b2635]/30 focus:border-[#8b2635] transition-all duration-200"
                    autoFocus
                  />
                </div>
                
                <div className="flex gap-3 pt-4">
                  <Button
                    onClick={() => setShowAddBlockModal(false)}
                    variant="outline"
                    className="flex-1 border-[#e0d7cc] text-[#8b7355] hover:bg-[#e0d7cc]/20 rounded-xl h-12 font-poppins"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={createSubBlock}
                    disabled={!newBlockName.trim()}
                    className="flex-1 bg-gradient-to-r from-[#8b2635] to-[#6d1f2c] hover:from-[#6d1f2c] hover:to-[#8b2635] text-white border-0 rounded-xl h-12 font-poppins disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Create Block
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Add Content Modal */}
        {showAddContentModal && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[9999]">
            <div className="bg-white/90 backdrop-blur-md border border-white/30 rounded-2xl shadow-2xl w-full max-w-2xl mx-4 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-cormorant font-bold text-[#2E0406]">Add Content</h2>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowAddContentModal(false)}
                  className="w-8 h-8 rounded-full hover:bg-[#8b2635]/10 transition-all duration-200"
                >
                  <X className="w-4 h-4 text-[#8b2635]" />
                </Button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-poppins font-medium text-[#2E0406] mb-2">
                    Content Title
                  </label>
                  <input
                    type="text"
                    value={newContentTitle}
                    onChange={(e) => setNewContentTitle(e.target.value)}
                    placeholder="Enter content title..."
                    className="w-full px-4 py-3 bg-white/60 border border-[#e0d7cc]/40 rounded-xl text-[#2E0406] placeholder-[#8b7355] font-poppins focus:outline-none focus:ring-2 focus:ring-[#8b2635]/30 focus:border-[#8b2635] transition-all duration-200"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-poppins font-medium text-[#2E0406] mb-2">
                    Content
                  </label>
                  <textarea
                    value={newContentText}
                    onChange={(e) => setNewContentText(e.target.value)}
                    placeholder="Enter your content..."
                    rows={6}
                    className="w-full px-4 py-3 bg-white/60 border border-[#e0d7cc]/40 rounded-xl text-[#2E0406] placeholder-[#8b7355] font-poppins focus:outline-none focus:ring-2 focus:ring-[#8b2635]/30 focus:border-[#8b2635] transition-all duration-200 resize-none"
                  />
                </div>
                
                <div className="flex gap-3 pt-4">
                  <Button
                    onClick={() => setShowAddContentModal(false)}
                    variant="outline"
                    className="flex-1 border-[#e0d7cc] text-[#8b7355] hover:bg-[#e0d7cc]/20 rounded-xl h-12 font-poppins"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={() => {
                      // TODO: Implement content creation
                      setShowAddContentModal(false);
                      setNewContentTitle("");
                      setNewContentText("");
                    }}
                    disabled={!newContentTitle.trim() || !newContentText.trim()}
                    className="flex-1 bg-gradient-to-r from-[#8b2635] to-[#6d1f2c] hover:from-[#6d1f2c] hover:to-[#8b2635] text-white border-0 rounded-xl h-12 font-poppins disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Add Content
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};
