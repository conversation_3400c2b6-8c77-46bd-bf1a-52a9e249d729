import { supabase } from './supabase';
import { ensureTagDetectionRulesTable } from './initializeDatabase';

// Interface for tag detection rules
interface TagRule {
  id: string;
  tag_name: string;
  keywords: string[];
  priority: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Cache for tag rules to avoid frequent database calls
let tagRulesCache: TagRule[] | null = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Fallback rules when database is not available
const getFallbackRules = (): TagRule[] => {
  return [
    {
      id: 'fallback-health',
      tag_name: 'Health & Medicine',
      keywords: [
        'medicine', 'medical', 'health', 'doctor', 'hospital', 'pharmacy',
        'drug', 'medication', 'treatment', 'therapy', 'diagnosis', 'symptom',
        'disease', 'illness', 'wellness', 'fitness', 'nutrition', 'diet',
        'exercise', 'mental health', 'psychology', 'psychiatry', 'surgery',
        'clinic', 'nursing', 'anatomy', 'physiology', 'pathology', 'radiology',
        'cardiology', 'neurology', 'oncology', 'pediatric', 'geriatric',
        'dental', 'ophthalmology', 'dermatology', 'orthopedic', 'vaccine',
        'immunology', 'epidemiology', 'pharmacology'
      ],
      priority: 10,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'fallback-science',
      tag_name: 'Science',
      keywords: [
        'science', 'physics', 'chemistry', 'biology', 'quantum', 'molecular',
        'genetics', 'astronomy', 'research', 'experiment', 'laboratory'
      ],
      priority: 9,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'fallback-technology',
      tag_name: 'Technology',
      keywords: [
        'technology', 'programming', 'coding', 'software', 'computer',
        'ai', 'artificial intelligence', 'machine learning', 'data science'
      ],
      priority: 8,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'fallback-business',
      tag_name: 'Business',
      keywords: [
        'business', 'marketing', 'finance', 'management', 'economics', 'startup'
      ],
      priority: 7,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  ];
};

// Initialize default tag rules in the database
export const initializeDefaultTagRules = async (): Promise<void> => {
  try {
    // Ensure the table exists first
    const tableExists = await ensureTagDetectionRulesTable();
    if (!tableExists) {
      console.log('tag_detection_rules table does not exist. Using fallback detection.');
      return;
    }

    // Check if rules already exist
    const { data: existingRules, error: checkError } = await supabase
      .from('tag_detection_rules')
      .select('id')
      .limit(1);

    if (checkError) {
      console.error('Error checking existing tag rules:', checkError);
      return;
    }

    // If rules already exist, don't initialize
    if (existingRules && existingRules.length > 0) {
      console.log('Tag detection rules already exist, skipping initialization');
      return;
    }

    // Default tag rules with comprehensive keywords
    const defaultRules: Omit<TagRule, 'id' | 'created_at' | 'updated_at'>[] = [
      {
        tag_name: 'Health & Medicine',
        keywords: [
          'medicine', 'medical', 'health', 'doctor', 'hospital', 'pharmacy',
          'drug', 'medication', 'treatment', 'therapy', 'diagnosis', 'symptom',
          'disease', 'illness', 'wellness', 'fitness', 'nutrition', 'diet',
          'exercise', 'mental health', 'psychology', 'psychiatry', 'surgery',
          'clinic', 'nursing', 'anatomy', 'physiology', 'pathology', 'radiology',
          'cardiology', 'neurology', 'oncology', 'pediatric', 'geriatric',
          'dental', 'ophthalmology', 'dermatology', 'orthopedic', 'vaccine',
          'immunology', 'epidemiology', 'pharmacology', 'healthcare', 'patient',
          'medical record', 'prescription', 'dosage', 'side effect', 'allergy'
        ],
        priority: 10,
        is_active: true
      },
      {
        tag_name: 'Science',
        keywords: [
          'science', 'physics', 'chemistry', 'biology', 'quantum', 'molecular',
          'genetics', 'astronomy', 'research', 'experiment', 'laboratory',
          'scientific', 'theory', 'hypothesis', 'data', 'analysis', 'microscope',
          'telescope', 'dna', 'rna', 'protein', 'cell', 'organism', 'evolution',
          'ecosystem', 'environment', 'climate', 'geology', 'mathematics',
          'statistics', 'algorithm', 'formula', 'equation'
        ],
        priority: 9,
        is_active: true
      },
      {
        tag_name: 'Technology',
        keywords: [
          'technology', 'programming', 'coding', 'software', 'computer',
          'ai', 'artificial intelligence', 'machine learning', 'data science',
          'web development', 'mobile app', 'database', 'cloud', 'cybersecurity',
          'blockchain', 'cryptocurrency', 'iot', 'internet of things',
          'automation', 'robotics', 'virtual reality', 'augmented reality',
          'javascript', 'python', 'java', 'react', 'nodejs', 'api',
          'framework', 'library', 'github', 'deployment'
        ],
        priority: 8,
        is_active: true
      },
      {
        tag_name: 'Business',
        keywords: [
          'business', 'marketing', 'finance', 'management', 'economics',
          'startup', 'entrepreneur', 'investment', 'revenue', 'profit',
          'strategy', 'planning', 'budget', 'accounting', 'sales',
          'customer', 'client', 'market', 'competition', 'brand',
          'advertising', 'promotion', 'leadership', 'team', 'project',
          'meeting', 'presentation', 'proposal', 'contract', 'negotiation'
        ],
        priority: 7,
        is_active: true
      },
      {
        tag_name: 'Education',
        keywords: [
          'education', 'learning', 'study', 'school', 'university', 'college',
          'course', 'lesson', 'tutorial', 'training', 'workshop', 'seminar',
          'lecture', 'exam', 'test', 'assignment', 'homework', 'research',
          'thesis', 'dissertation', 'degree', 'certificate', 'skill',
          'knowledge', 'teaching', 'instructor', 'professor', 'student',
          'curriculum', 'syllabus', 'academic', 'scholarship'
        ],
        priority: 6,
        is_active: true
      },
      {
        tag_name: 'Art & Design',
        keywords: [
          'art', 'design', 'creative', 'painting', 'drawing', 'sculpture',
          'photography', 'graphic design', 'illustration', 'animation',
          'video', 'film', 'music', 'audio', 'visual', 'aesthetic',
          'color', 'composition', 'typography', 'layout', 'ui', 'ux',
          'user interface', 'user experience', 'portfolio', 'gallery',
          'exhibition', 'artist', 'designer', 'canvas', 'digital art'
        ],
        priority: 5,
        is_active: true
      },
      {
        tag_name: 'History',
        keywords: [
          'history', 'historical', 'ancient', 'civilization', 'culture',
          'war', 'empire', 'dynasty', 'medieval', 'renaissance', 'revolution',
          'timeline', 'era', 'period', 'century', 'decade', 'archaeology',
          'artifact', 'monument', 'heritage', 'tradition', 'legacy',
          'biography', 'documentary', 'archive', 'museum', 'historical figure',
          'event', 'battle', 'treaty', 'discovery', 'exploration'
        ],
        priority: 4,
        is_active: true
      },
      {
        tag_name: 'Literature',
        keywords: [
          'literature', 'book', 'novel', 'story', 'poetry', 'poem',
          'writing', 'author', 'writer', 'reading', 'text', 'manuscript',
          'publication', 'publisher', 'editor', 'chapter', 'character',
          'plot', 'theme', 'genre', 'fiction', 'non-fiction', 'biography',
          'autobiography', 'essay', 'article', 'journal', 'magazine',
          'newspaper', 'review', 'critique', 'analysis'
        ],
        priority: 3,
        is_active: true
      },
      {
        tag_name: 'Personal',
        keywords: [
          'personal', 'diary', 'journal', 'thoughts', 'reflection',
          'goals', 'dreams', 'aspirations', 'memories', 'experiences',
          'feelings', 'emotions', 'relationships', 'family', 'friends',
          'hobby', 'interest', 'passion', 'lifestyle', 'routine',
          'habit', 'self-improvement', 'mindfulness', 'meditation',
          'gratitude', 'achievement', 'milestone', 'celebration'
        ],
        priority: 2,
        is_active: true
      }
    ];

    // Insert default rules
    const { error: insertError } = await supabase
      .from('tag_detection_rules')
      .insert(defaultRules);

    if (insertError) {
      console.error('Error inserting default tag rules:', insertError);
    } else {
      console.log('Default tag detection rules initialized successfully');
      // Clear cache to force reload
      tagRulesCache = null;
    }
  } catch (error) {
    console.error('Error initializing default tag rules:', error);
  }
};

// Load tag rules from database with caching
export const loadTagRules = async (): Promise<TagRule[]> => {
  const now = Date.now();

  // Return cached rules if they're still valid
  if (tagRulesCache && (now - cacheTimestamp) < CACHE_DURATION) {
    return tagRulesCache;
  }

  try {
    // Ensure table exists first
    const tableExists = await ensureTagDetectionRulesTable();
    if (!tableExists) {
      return getFallbackRules();
    }

    const { data, error } = await supabase
      .from('tag_detection_rules')
      .select('*')
      .eq('is_active', true)
      .order('priority', { ascending: false });

    if (error) {
      console.error('Error loading tag rules:', error);
      return getFallbackRules();
    }

    // Update cache
    tagRulesCache = data || [];
    cacheTimestamp = now;

    return tagRulesCache;
  } catch (error) {
    console.error('Error loading tag rules:', error);
    return getFallbackRules();
  }
};

// Smart tag detection using database rules
export const detectSmartTag = async (blockName: string): Promise<string> => {
  if (!blockName || !blockName.trim()) {
    return 'General';
  }

  try {
    // Try to load fresh rules from database
    const rules = await loadTagRules();
    const name = blockName.toLowerCase().trim();

    // Find the first matching rule (rules are ordered by priority)
    for (const rule of rules) {
      if (rule && rule.keywords && Array.isArray(rule.keywords)) {
        for (const keyword of rule.keywords) {
          if (keyword && name.includes(keyword.toLowerCase())) {
            return rule.tag_name || 'General';
          }
        }
      }
    }

    return 'General';
  } catch (error) {
    console.error('Error in smart tag detection:', error);
    // Fallback to sync version
    return detectSmartTagSync(blockName);
  }
};

// Synchronous version for immediate UI feedback (uses cache)
export const detectSmartTagSync = (blockName: string): string => {
  try {
    if (!blockName || !blockName.trim()) {
      return 'General';
    }

    // Use cached rules if available, otherwise use fallback rules
    const rules = tagRulesCache || getFallbackRules();
    const name = blockName.toLowerCase().trim();

    // Find the first matching rule (rules are ordered by priority)
    for (const rule of rules) {
      if (rule && rule.keywords && Array.isArray(rule.keywords)) {
        for (const keyword of rule.keywords) {
          if (keyword && name.includes(keyword.toLowerCase())) {
            return rule.tag_name || 'General';
          }
        }
      }
    }

    return 'General';
  } catch (error) {
    console.error('Error in detectSmartTagSync:', error);
    return 'General';
  }
};

// Clear cache (useful for admin updates)
export const clearTagRulesCache = (): void => {
  tagRulesCache = null;
  cacheTimestamp = 0;
};

// Add a new tag rule
export const addTagRule = async (rule: Omit<TagRule, 'id' | 'created_at' | 'updated_at'>): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('tag_detection_rules')
      .insert([rule]);

    if (error) {
      console.error('Error adding tag rule:', error);
      return false;
    }

    // Clear cache to force reload
    clearTagRulesCache();
    return true;
  } catch (error) {
    console.error('Error adding tag rule:', error);
    return false;
  }
};

// Update an existing tag rule
export const updateTagRule = async (id: string, updates: Partial<TagRule>): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('tag_detection_rules')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id);

    if (error) {
      console.error('Error updating tag rule:', error);
      return false;
    }

    // Clear cache to force reload
    clearTagRulesCache();
    return true;
  } catch (error) {
    console.error('Error updating tag rule:', error);
    return false;
  }
};
