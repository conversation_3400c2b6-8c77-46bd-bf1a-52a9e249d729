-- Populate tag detection rules with comprehensive keywords
-- Run this in your Supabase SQL Editor after creating the tag_detection_rules table

-- Clear existing rules (optional - remove this line if you want to keep existing rules)
-- DELETE FROM tag_detection_rules;

-- Insert comprehensive tag detection rules
INSERT INTO tag_detection_rules (tag_name, keywords, priority, is_active) VALUES
(
  'Health & Medicine',
  ARRAY[
    'medicine', 'medical', 'health', 'doctor', 'hospital', 'pharmacy',
    'drug', 'medication', 'treatment', 'therapy', 'diagnosis', 'symptom',
    'disease', 'illness', 'wellness', 'fitness', 'nutrition', 'diet',
    'exercise', 'mental health', 'psychology', 'psychiatry', 'surgery',
    'clinic', 'nursing', 'anatomy', 'physiology', 'pathology', 'radiology',
    'cardiology', 'neurology', 'oncology', 'pediatric', 'geriatric',
    'dental', 'ophthalmology', 'dermatology', 'orthopedic', 'vaccine',
    'immunology', 'epidemiology', 'pharmacology', 'healthcare', 'patient'
  ],
  10,
  true
),
(
  'Technology',
  ARRAY[
    'technology', 'tech', 'computer', 'software', 'hardware', 'programming',
    'coding', 'development', 'app', 'application', 'website', 'web',
    'internet', 'digital', 'online', 'cyber', 'data', 'database',
    'algorithm', 'artificial intelligence', 'ai', 'machine learning',
    'blockchain', 'cryptocurrency', 'cloud', 'server', 'network',
    'security', 'cybersecurity', 'mobile', 'smartphone', 'tablet',
    'laptop', 'desktop', 'gaming', 'virtual reality', 'vr', 'ar'
  ],
  9,
  true
),
(
  'Education',
  ARRAY[
    'education', 'learning', 'study', 'school', 'university', 'college',
    'course', 'class', 'lesson', 'tutorial', 'training', 'teaching',
    'student', 'teacher', 'professor', 'academic', 'research',
    'knowledge', 'skill', 'certification', 'degree', 'diploma',
    'exam', 'test', 'quiz', 'homework', 'assignment', 'project',
    'thesis', 'dissertation', 'scholarship', 'curriculum', 'syllabus'
  ],
  8,
  true
),
(
  'Business',
  ARRAY[
    'business', 'company', 'corporate', 'enterprise', 'startup',
    'entrepreneur', 'management', 'marketing', 'sales', 'finance',
    'accounting', 'investment', 'profit', 'revenue', 'budget',
    'strategy', 'planning', 'meeting', 'presentation', 'project',
    'team', 'leadership', 'hr', 'human resources', 'recruitment',
    'client', 'customer', 'service', 'product', 'brand', 'market'
  ],
  7,
  true
),
(
  'Art & Creativity',
  ARRAY[
    'art', 'creative', 'creativity', 'design', 'drawing', 'painting',
    'sculpture', 'photography', 'music', 'singing', 'dancing',
    'writing', 'poetry', 'literature', 'novel', 'story', 'theater',
    'drama', 'film', 'movie', 'cinema', 'animation', 'illustration',
    'graphic design', 'fashion', 'craft', 'handmade', 'diy',
    'artistic', 'aesthetic', 'beautiful', 'inspiration', 'imagination'
  ],
  6,
  true
),
(
  'Travel',
  ARRAY[
    'travel', 'trip', 'vacation', 'holiday', 'journey', 'adventure',
    'destination', 'flight', 'hotel', 'accommodation', 'booking',
    'tourism', 'tourist', 'sightseeing', 'explore', 'discovery',
    'country', 'city', 'culture', 'local', 'international',
    'passport', 'visa', 'luggage', 'backpack', 'itinerary',
    'guide', 'map', 'restaurant', 'food', 'cuisine', 'beach'
  ],
  5,
  true
),
(
  'Food & Cooking',
  ARRAY[
    'food', 'cooking', 'recipe', 'kitchen', 'chef', 'restaurant',
    'meal', 'breakfast', 'lunch', 'dinner', 'snack', 'ingredient',
    'spice', 'flavor', 'taste', 'delicious', 'cuisine', 'dish',
    'baking', 'grilling', 'frying', 'boiling', 'healthy eating',
    'nutrition', 'diet', 'vegetarian', 'vegan', 'organic',
    'fresh', 'homemade', 'gourmet', 'culinary', 'beverage', 'drink'
  ],
  4,
  true
),
(
  'Personal',
  ARRAY[
    'personal', 'diary', 'journal', 'thoughts', 'reflection',
    'goals', 'dreams', 'aspirations', 'memories', 'experiences',
    'feelings', 'emotions', 'relationships', 'family', 'friends',
    'hobby', 'interest', 'passion', 'lifestyle', 'routine',
    'habit', 'self-improvement', 'mindfulness', 'meditation',
    'gratitude', 'achievement', 'milestone', 'celebration'
  ],
  2,
  true
);

-- Verify the insertion
SELECT tag_name, array_length(keywords, 1) as keyword_count, priority 
FROM tag_detection_rules 
ORDER BY priority DESC;
