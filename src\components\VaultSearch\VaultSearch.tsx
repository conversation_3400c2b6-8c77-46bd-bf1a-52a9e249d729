import React, { useState, useEffect, useRef } from 'react';
import { Search, X, Filter, ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';
import { Button } from '../ui/button';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../../hooks/useAuth';

interface VaultSearchProps {
  onSearchChange?: (query: string, filter: FilterType, tag: string) => void;
  onSortChange?: (sortBy: SortType, sortOrder: SortOrder) => void;
}

export type FilterType = 'all' | 'blocks' | 'content' | 'tag';
export type SortType = 'name' | 'date' | 'tag' | 'items';
export type SortOrder = 'asc' | 'desc';

export const VaultSearch: React.FC<VaultSearchProps> = ({ onSearchChange, onSortChange }) => {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [activeFilter, setActiveFilter] = useState<FilterType>('all');
  const [selectedTag, setSelectedTag] = useState<string>('');
  const [availableTags, setAvailableTags] = useState<string[]>([]);
  const [showSort, setShowSort] = useState(false);
  const [sortBy, setSortBy] = useState<SortType>(() => {
    // Load from localStorage on initialization
    const saved = localStorage.getItem('vaultSortBy');
    return (saved as SortType) || 'date';
  });
  const [sortOrder, setSortOrder] = useState<SortOrder>(() => {
    // Load from localStorage on initialization
    const saved = localStorage.getItem('vaultSortOrder');
    return (saved as SortOrder) || 'desc';
  });
  const inputRef = useRef<HTMLInputElement>(null);

  // Notify parent component when search changes
  useEffect(() => {
    if (onSearchChange) {
      onSearchChange(searchQuery, activeFilter, selectedTag);
    }
  }, [searchQuery, activeFilter, selectedTag, onSearchChange]);

  // Notify parent component when sort changes and persist to localStorage
  useEffect(() => {
    if (onSortChange) {
      onSortChange(sortBy, sortOrder);
    }
    // Persist sort preferences
    localStorage.setItem('vaultSortBy', sortBy);
    localStorage.setItem('vaultSortOrder', sortOrder);
  }, [sortBy, sortOrder, onSortChange]);

  // Load available tags for filtering
  useEffect(() => {
    if (user) {
      loadAvailableTags();
    }
  }, [user]);

  const loadAvailableTags = async () => {
    if (!user) return;

    try {
      // Load all blocks to extract tags
      const { data: blocksData, error: blocksError } = await supabase
        .from('vault_blocks')
        .select('smart_tag')
        .eq('user_id', user.uid);

      if (blocksError) {
        console.error('Error loading tags:', blocksError);
      } else {
        // Extract unique tags
        const tags = [...new Set((blocksData || []).map(block => block.smart_tag).filter(Boolean))];
        setAvailableTags(tags);
      }
    } catch (error) {
      console.error('Error loading tags:', error);
    }
  };

  const clearSearch = () => {
    setSearchQuery('');
    setActiveFilter('all');
    setSelectedTag('');
    setShowFilters(false);
    setShowSort(false);
    inputRef.current?.focus();
  };

  const handleSortChange = (newSortBy: SortType) => {
    if (sortBy === newSortBy) {
      // Toggle order if same sort type
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      // New sort type, default to descending for date/items, ascending for name/tag
      setSortBy(newSortBy);
      setSortOrder(newSortBy === 'name' || newSortBy === 'tag' ? 'asc' : 'desc');
    }
  };

  return (
    <div className="relative w-full max-w-3xl mx-auto">
      {/* Search Input with Filter and Sort - Centered Layout */}
      <div className="flex justify-center gap-3" id="search-container">
        <div className="relative w-96">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[#8b7355] z-10 pointer-events-none" />
          <input
            ref={inputRef}
            type="text"
            placeholder="Search blocks and content..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-12 pr-12 py-3 bg-white/80 backdrop-blur-sm border border-[#d4c7b8]/40 rounded-2xl text-[#2E0406] placeholder:text-[#8b7355]/60 font-poppins text-base focus:outline-none focus:ring-2 focus:ring-[#8b2635]/30 focus:border-[#8b2635]/50 focus:bg-white/90 transition-all duration-200 shadow-sm hover:shadow-md"
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="icon"
              onClick={clearSearch}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 rounded-full hover:bg-[#8b2635]/10 transition-all duration-200"
            >
              <X className="w-4 h-4 text-[#8b7355]" />
            </Button>
          )}
        </div>

        {/* Filter Button */}
        <Button
          variant="ghost"
          onClick={() => {
            setShowFilters(!showFilters);
            if (!showFilters) setShowSort(false); // Close sort when opening filter
          }}
          className={`px-4 py-3 h-12 rounded-2xl border transition-all duration-200 ${
            showFilters || activeFilter !== 'all'
              ? 'bg-[#8b2635]/10 border-[#8b2635]/30 text-[#8b2635]'
              : 'bg-white/80 border-[#d4c7b8]/40 text-[#8b7355] hover:bg-[#8b2635]/5'
          }`}
        >
          <Filter className="w-5 h-5" />
        </Button>

        {/* Sort Button */}
        <Button
          variant="ghost"
          onClick={() => {
            setShowSort(!showSort);
            if (!showSort) setShowFilters(false); // Close filter when opening sort
          }}
          className={`px-4 py-3 h-12 rounded-2xl border transition-all duration-200 ${
            showSort
              ? 'bg-[#8b2635]/10 border-[#8b2635]/30 text-[#8b2635]'
              : 'bg-white/80 border-[#d4c7b8]/40 text-[#8b7355] hover:bg-[#8b2635]/5'
          }`}
        >
          {sortOrder === 'asc' ? (
            <ArrowUp className="w-5 h-5" />
          ) : (
            <ArrowDown className="w-5 h-5" />
          )}
        </Button>
      </div>

      {/* Filter Panel */}
      {showFilters && (
        <div className="mt-3 p-4 bg-white/90 backdrop-blur-sm border border-[#d4c7b8]/40 rounded-2xl shadow-sm" style={{ width: '512px', margin: '12px auto 0 auto' }}>
          <div className="flex flex-wrap gap-3">
            <div className="flex items-center gap-2">
              <span className="text-sm font-poppins font-medium text-[#2E0406]">Filter by:</span>
            </div>

            {/* Filter Type Buttons */}
            {[
              { key: 'all', label: 'All' },
              { key: 'blocks', label: 'Blocks Only' },
              { key: 'content', label: 'Content Only' },
              { key: 'tag', label: 'By Tag' }
            ].map((filter) => (
              <Button
                key={filter.key}
                variant="ghost"
                size="sm"
                onClick={() => {
                  setActiveFilter(filter.key as FilterType);
                  if (filter.key !== 'tag') setSelectedTag('');
                }}
                className={`px-3 py-1.5 rounded-xl text-xs font-poppins transition-all duration-200 ${
                  activeFilter === filter.key
                    ? 'bg-[#8b2635] text-white hover:bg-[#6d1f2c]'
                    : 'bg-[#8b7355]/10 text-[#8b7355] hover:bg-[#8b2635]/10 hover:text-[#8b2635]'
                }`}
              >
                {filter.label}
              </Button>
            ))}

            {/* Tag Selector */}
            {activeFilter === 'tag' && (
              <select
                value={selectedTag}
                onChange={(e) => setSelectedTag(e.target.value)}
                className="px-3 py-1.5 bg-white/80 border border-[#d4c7b8]/40 rounded-xl text-xs font-poppins text-[#2E0406] focus:outline-none focus:ring-2 focus:ring-[#8b2635]/30"
              >
                <option value="">Select a tag...</option>
                {availableTags.map((tag) => (
                  <option key={tag} value={tag}>{tag}</option>
                ))}
              </select>
            )}
          </div>
        </div>
      )}

      {/* Sort Panel */}
      {showSort && (
        <div className="mt-3 p-4 bg-white/90 backdrop-blur-sm border border-[#d4c7b8]/40 rounded-2xl shadow-sm" style={{ width: '512px', margin: '12px auto 0 auto' }}>
          <div className="flex flex-wrap gap-3">
            <div className="flex items-center gap-2">
              <span className="text-sm font-poppins font-medium text-[#2E0406]">Sort by:</span>
            </div>

            {/* Sort Options */}
            {[
              { key: 'date', label: 'Date Created' },
              { key: 'name', label: 'Name' },
              { key: 'tag', label: 'Tag' },
              { key: 'items', label: 'Item Count' }
            ].map((sort) => (
              <Button
                key={sort.key}
                variant="ghost"
                size="sm"
                onClick={() => handleSortChange(sort.key as SortType)}
                className={`px-3 py-1.5 rounded-xl text-xs font-poppins transition-all duration-200 flex items-center gap-1 ${
                  sortBy === sort.key
                    ? 'bg-[#8b2635] text-white hover:bg-[#6d1f2c]'
                    : 'bg-[#8b7355]/10 text-[#8b7355] hover:bg-[#8b2635]/10 hover:text-[#8b2635]'
                }`}
              >
                {sort.label}
                {sortBy === sort.key && (
                  sortOrder === 'asc' ? (
                    <ArrowUp className="w-3 h-3" />
                  ) : (
                    <ArrowDown className="w-3 h-3" />
                  )
                )}
              </Button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};



